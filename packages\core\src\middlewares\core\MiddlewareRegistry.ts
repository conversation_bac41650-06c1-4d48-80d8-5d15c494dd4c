import { Context } from "koishi";
import { MiddlewareDefinition, IMiddleware } from "./MiddlewareCore";

/**
 * 中间件注册表
 */
export class MiddlewareRegistry {
    private definitions = new Map<string, MiddlewareDefinition>();
    private instances = new Map<string, IMiddleware>();

    constructor(private ctx: Context) {}

    /**
     * 注册中间件定义
     */
    register<TConfig = any>(id: string, definition: MiddlewareDefinition<TConfig>): void {
        if (this.definitions.has(id)) {
            throw new Error(`中间件 ${id} 已经注册`);
        }

        this.definitions.set(id, definition);
        this.ctx.logger.debug(`中间件 ${id} 注册成功`);
    }

    /**
     * 创建中间件实例
     */
    create<TConfig = any>(id: string, config?: TConfig): IMiddleware<TConfig> {
        const definition = this.definitions.get(id);
        if (!definition) {
            throw new Error(`未找到中间件定义: ${id}`);
        }

        const finalConfig = { ...definition.defaultConfig, ...config };

        // 验证配置
        if (definition.validateConfig && !definition.validateConfig(finalConfig)) {
            throw new Error(`中间件 ${id} 配置验证失败`);
        }

        const instance = definition.factory(this.ctx, finalConfig);
        this.instances.set(id, instance);

        return instance;
    }

    /**
     * 获取中间件实例
     */
    get(id: string): IMiddleware | undefined {
        return this.instances.get(id);
    }

    /**
     * 获取所有注册的中间件定义
     */
    getDefinitions(): Map<string, MiddlewareDefinition> {
        return new Map(this.definitions);
    }

    /**
     * 获取所有中间件实例
     */
    getInstances(): Map<string, IMiddleware> {
        return new Map(this.instances);
    }

    /**
     * 清理所有实例
     */
    async dispose(): Promise<void> {
        for (const [id, instance] of this.instances) {
            if (instance.dispose) {
                try {
                    await instance.dispose();
                } catch (error) {
                    this.ctx.logger.error(`清理中间件 ${id} 失败:`, error);
                }
            }
        }
        this.instances.clear();
    }
}
