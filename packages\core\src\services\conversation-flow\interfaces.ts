/**
 * ConversationFlowAnalyzer 模块化接口定义
 *
 * 本文件定义了重构后各模块间的清晰接口，确保低耦合度和高内聚性
 */

import { ChatMessage } from "../../shared/database";

// ==================== 核心数据接口 ====================

/**
 * 分析上下文
 */
export interface AnalysisContext {
    channelId: string;
    message: ChatMessage;
    history: MessageHistory;
    userProfiles: Map<string, UserProfile>;
    timestamp: Date;
}

/**
 * 消息历史
 */
export interface MessageHistory {
    recentMessages: ChatMessage[];
    messageCount: number;
    timeSpan: number; // 时间跨度（毫秒）
}

/**
 * 用户画像
 */
export interface UserProfile {
    userId: string;
    messageCount: number;
    lastActiveTime: Date;
    averageInterval: number;
    participationScore: number;
    isActiveParticipant: boolean;
    interests: string[];
    communicationStyle: 'active' | 'passive' | 'lurker';
}

/**
 * 分析结果基类
 */
export interface AnalysisResult {
    confidence: number;
    reasoning: string;
    timestamp: Date;
    metadata: Record<string, any>;
}

// ==================== 话题分析接口 ====================

/**
 * 话题信息
 */
export interface TopicInfo {
    topicId: string;
    status: 'developing' | 'stable' | 'cooling' | 'ended';
    keywords: string[];
    participants: Set<string>;
    dominantParticipants: string[];
    messageCount: number;
    lastActivity: Date;
    stability: number;
    engagementLevel: number;
    coherence: number;
    semanticVector?: number[]; // embedding向量
}

/**
 * 话题分析结果
 */
export interface TopicAnalysisResult extends AnalysisResult {
    activeTopics: TopicInfo[];
    newTopics: TopicInfo[];
    endedTopics: string[];
    topicTransitions: TopicTransition[];
    overallCoherence: number;
}

/**
 * 话题转换
 */
export interface TopicTransition {
    fromTopicId?: string;
    toTopicId: string;
    transitionType: 'continuation' | 'shift' | 'split' | 'merge' | 'new';
    confidence: number;
}

/**
 * 话题更新
 */
export interface TopicUpdate {
    keywords?: string[];
    status?: TopicInfo['status'];
    participants?: Set<string>;
    metadata?: Record<string, any>;
}

/**
 * 话题分析器接口
 */
export interface ITopicAnalyzer {
    /**
     * 分析话题
     */
    analyzeTopics(context: AnalysisContext): Promise<TopicAnalysisResult>;

    /**
     * 更新话题状态
     */
    updateTopicState(topicId: string, update: TopicUpdate): void;

    /**
     * 获取活跃话题
     */
    getActiveTopics(channelId: string): TopicInfo[];

    /**
     * 计算话题相似度
     */
    calculateTopicSimilarity(topic1: TopicInfo, topic2: TopicInfo): Promise<number>;

    /**
     * 合并相似话题
     */
    mergeSimilarTopics(channelId: string, threshold: number): Promise<void>;
}

// ==================== 用户活跃度分析接口 ====================

/**
 * 频道动态
 */
export interface ChannelDynamics {
    totalParticipants: number;
    activeParticipants: number;
    conversationIntensity: number;
    topicSwitchFrequency: number;
    averageResponseTime: number;
    peakActivityPeriods: TimeRange[];
}

/**
 * 时间范围
 */
export interface TimeRange {
    start: Date;
    end: Date;
    intensity: number;
}

/**
 * 活跃度分析结果
 */
export interface ActivityAnalysisResult extends AnalysisResult {
    userProfile: UserProfile;
    channelDynamics: ChannelDynamics;
    participationTrend: 'increasing' | 'stable' | 'decreasing';
    influenceScore: number;
    engagementPrediction: number;
}

/**
 * 用户活跃度跟踪器接口
 */
export interface IUserActivityTracker {
    /**
     * 跟踪用户活跃度
     */
    trackActivity(context: AnalysisContext): Promise<ActivityAnalysisResult>;

    /**
     * 获取用户画像
     */
    getUserProfile(userId: string): UserProfile | null;

    /**
     * 获取频道动态
     */
    getChannelDynamics(channelId: string): ChannelDynamics;

    /**
     * 预测用户参与度
     */
    predictEngagement(userId: string, channelId: string): Promise<number>;

    /**
     * 识别影响力用户
     */
    identifyInfluencers(channelId: string): string[];
}

// ==================== 节奏分析接口 ====================

/**
 * 节奏分析结果
 */
export interface PaceAnalysisResult extends AnalysisResult {
    currentPace: 'fast' | 'normal' | 'slow';
    paceStability: number;
    burstDetected: boolean;
    optimalResponseWindow: TimeWindow;
    paceHistory: PaceSnapshot[];
}

/**
 * 时间窗口
 */
export interface TimeWindow {
    start: Date;
    end: Date;
    confidence: number;
}

/**
 * 节奏快照
 */
export interface PaceSnapshot {
    timestamp: Date;
    pace: 'fast' | 'normal' | 'slow';
    messageCount: number;
    averageInterval: number;
}

/**
 * 时机预测
 */
export interface TimingPrediction {
    suggestedWaitTime: number;
    confidence: number;
    reasoning: string;
    alternativeTimings: number[];
}

/**
 * 节奏分析器接口
 */
export interface IPaceAnalyzer {
    /**
     * 分析对话节奏
     */
    analyzePace(context: AnalysisContext): Promise<PaceAnalysisResult>;

    /**
     * 预测最佳时机
     */
    predictOptimalTiming(channelId: string): Promise<TimingPrediction>;

    /**
     * 检测消息爆发
     */
    detectBurst(messages: ChatMessage[]): boolean;

    /**
     * 计算节奏稳定性
     */
    calculatePaceStability(channelId: string): number;
}

// ==================== 状态管理接口 ====================

/**
 * 对话状态
 */
export interface ConversationState {
    channelId: string;
    topics: Map<string, TopicInfo>;
    userProfiles: Map<string, UserProfile>;
    channelDynamics: ChannelDynamics;
    lastUpdated: Date;
    version: number;
}

/**
 * 性能指标
 */
export interface PerformanceMetrics {
    memoryUsage: {
        totalSize: number;
        topicsSize: number;
        usersSize: number;
        messagesSize: number;
    };
    operationStats: {
        analysisCount: number;
        averageAnalysisTime: number;
        cacheHitRate: number;
        errorRate: number;
    };
    resourceUtilization: {
        cpuUsage: number;
        memoryUsage: number;
        diskUsage: number;
    };
}

/**
 * 状态管理器接口
 */
export interface IStateManager {
    /**
     * 保存状态
     */
    saveState(channelId: string, state: ConversationState): Promise<void>;

    /**
     * 加载状态
     */
    loadState(channelId: string): Promise<ConversationState | null>;

    /**
     * 优化内存
     */
    optimizeMemory(): Promise<void>;

    /**
     * 获取性能指标
     */
    getMetrics(): PerformanceMetrics;

    /**
     * 清理过期数据
     */
    cleanup(maxAge: number): Promise<void>;

    /**
     * 创建状态快照
     */
    createSnapshot(channelId: string): Promise<string>;

    /**
     * 恢复状态快照
     */
    restoreSnapshot(channelId: string, snapshotId: string): Promise<void>;
}

// ==================== 决策引擎接口 ====================

/**
 * 回复决策
 */
export interface ReplyDecision {
    shouldReply: boolean;
    confidence: number;
    reasoning: string;
    suggestedWaitTime: number;
    priority: 'high' | 'medium' | 'low';
    tags: string[];
}

/**
 * 决策解释
 */
export interface DecisionExplanation {
    factors: DecisionFactor[];
    weights: Record<string, number>;
    finalScore: number;
    threshold: number;
    alternatives: AlternativeDecision[];
}

/**
 * 决策因子
 */
export interface DecisionFactor {
    name: string;
    value: number;
    weight: number;
    impact: 'positive' | 'negative' | 'neutral';
    description: string;
}

/**
 * 替代决策
 */
export interface AlternativeDecision {
    decision: ReplyDecision;
    probability: number;
    conditions: string[];
}

/**
 * 决策引擎接口
 */
export interface IDecisionEngine {
    /**
     * 制定决策
     */
    makeDecision(
        topicAnalysis: TopicAnalysisResult,
        activityAnalysis: ActivityAnalysisResult,
        paceAnalysis: PaceAnalysisResult
    ): Promise<ReplyDecision>;

    /**
     * 解释决策
     */
    explainDecision(decision: ReplyDecision): Promise<DecisionExplanation>;

    /**
     * 更新决策规则
     */
    updateRules(rules: DecisionRule[]): void;

    /**
     * 学习决策模式
     */
    learnFromFeedback(decision: ReplyDecision, feedback: DecisionFeedback): Promise<void>;
}

/**
 * 决策规则
 */
export interface DecisionRule {
    id: string;
    name: string;
    condition: string;
    action: string;
    weight: number;
    enabled: boolean;
}

/**
 * 决策反馈
 */
export interface DecisionFeedback {
    decisionId: string;
    actualOutcome: 'success' | 'failure' | 'neutral';
    userSatisfaction: number;
    contextFactors: Record<string, any>;
    timestamp: Date;
}

// ==================== 配置管理接口 ====================

/**
 * 话题分析器配置
 */
export interface TopicAnalyzerConfig {
    // 基础限制配置
    maxActiveTopicsCount: number;
    keywordExtractionLimit: number;

    // 话题状态阈值配置
    minMessagesForStableThreshold: number;
    minParticipantsForStableThreshold: number;

    // 时间配置（毫秒）
    topicTimeoutMs: number;
    topicCoolingMs: number;
    recentActivityWindowMs: number;

    // 相似度和合并配置
    topicSimilarityThreshold: number;
    topicMergeThreshold: number;

    // 稳定性计算权重
    stabilityWeights: {
        ageWeight: number;
        participantWeight: number;
        timeWeight: number;
        baseStability: number;
    };

    // 参与度计算配置
    engagementCalculation: {
        messageFrequencyDivisor: number;
        recentActivityWeight: number;
        messageFrequencyWeight: number;
    };

    // 连贯性调整配置
    coherenceAdjustments: {
        continuationBonus: number;
        shiftPenalty: number;
        minCoherence: number;
    };

    // 功能开关
    enableSemanticAnalysis: boolean;
    enableTopicMerging: boolean;

    // 文本处理配置
    textProcessing: {
        stopWords: string[];
        regexPatterns: {
            textCleaning: string;
            whitespaceNormalization: string;
        };
        topicIdRandomLength: number;
    };
}

/**
 * 用户活跃度跟踪器配置
 */
export interface UserActivityTrackerConfig {
    // 容量限制配置
    maxUserProfilesCount: number;
    maxInterestsPerUser: number;
    maxInfluencersCount: number;

    // 时间配置（毫秒）
    userProfileTimeoutMs: number;
    activityAnalysisWindowMs: number;
    conversationIntensityWindowMs: number;
    peakActivityDetectionWindowMs: number;

    // 阈值配置
    influenceScoreThreshold: number;
    peakActivityIntensityThreshold: number;
    minMessagesForProfileThreshold: number;

    // 衰减和权重配置
    engagementDecayRate: number;
    participationScoreWeights: {
        frequencyWeight: number;
        recencyWeight: number;
        consistencyWeight: number;
        influenceWeight: number;
    };

    // 参与度计算配置
    participationCalculation: {
        maxMessageCountForFrequency: number;
        idealResponseIntervalMs: number;
        conversationIntensityMultiplier: number;
        topicSwitchFrequencyDivisor: number;
    };

    // 交流风格判断配置
    communicationStyleThresholds: {
        activeMessageCountThreshold: number;
        activeIntervalThresholdMs: number;
        passiveMessageCountThreshold: number;
        passiveIntervalThresholdMs: number;
    };

    // 影响力计算配置
    influenceCalculation: {
        rankScoreWeight: number;
        activityScoreWeight: number;
        activeStyleMultiplier: number;
        passiveStyleMultiplier: number;
        lurkerStyleMultiplier: number;
    };

    // 趋势分析配置
    trendAnalysisThresholds: {
        increasingRecencyThreshold: number;
        increasingParticipationThreshold: number;
        decreasingRecencyThreshold: number;
        decreasingParticipationThreshold: number;
    };

    // 文本处理配置
    textProcessing: {
        stopWords: string[];
        keywordMinLength: number;
        regexPatterns: {
            textCleaning: string;
            whitespaceNormalization: string;
        };
    };
}

/**
 * 节奏分析器配置
 */
export interface PaceAnalyzerConfig {
    // 分析窗口配置
    analysisWindows: {
        shortTermWindowMs: number;
        mediumTermWindowMs: number;
        longTermWindowMs: number;
        paceCalculationWindowSize: number;
    };

    // 消息数量阈值
    messageCountThresholds: {
        minMessagesForAnalysis: number;
        burstDetectionThreshold: number;
        burstTimeWindowMs: number;
    };

    // 节奏分类阈值（毫秒）
    paceClassificationThresholds: {
        fastPaceThresholdMs: number;
        slowPaceThresholdMs: number;
        fastPaceMultiplier: number;
        slowPaceMultiplier: number;
    };

    // 动态阈值调整配置
    dynamicThresholdAdjustment: {
        groupSizeMultiplierBase: number;
        groupSizeMultiplierStep: number;
        maxGroupSizeMultiplier: number;
        minGroupSizeMultiplier: number;
        topicSwitchMultiplier: number;
    };

    // 参与度影响配置
    participationFactorWeights: {
        participationRatioWeight: number;
        conversationIntensityWeight: number;
    };

    // 节奏评估权重
    paceEvaluationWeights: {
        burstPenaltyMultiplier: number;
        variabilityPenaltyThreshold: number;
        variabilityPenaltyMultiplier: number;
        lowParticipationBonus: number;
        highParticipationPenalty: number;
        accelerationPenalty: number;
        decelerationBonus: number;
    };
}

/**
 * 状态管理器配置
 */
export interface StateManagerConfig {
    // 内存管理配置
    memoryManagement: {
        maxActiveTopicsPerChannel: number;
        maxUserActivitiesPerChannel: number;
        maxRecentMessagesCount: number;
        cleanupIntervalMs: number;
    };

    // 持久化配置
    persistence: {
        enableStatePersistence: boolean;
        snapshotIntervalMs: number;
        maxSnapshotsCount: number;
        compressionEnabled: boolean;
    };

    // 性能监控配置
    performanceMonitoring: {
        enableMetricsCollection: boolean;
        metricsUpdateIntervalMs: number;
        maxMetricsHistoryCount: number;
    };

    // 缓存配置
    caching: {
        enableCaching: boolean;
        cacheExpirationMs: number;
        maxCacheSize: number;
        cacheHitRateThreshold: number;
    };
}

/**
 * 决策引擎配置
 */
export interface DecisionEngineConfig {
    // 决策阈值配置
    decisionThresholds: {
        replyConfidenceThreshold: number;
        highPriorityThreshold: number;
        mediumPriorityThreshold: number;
    };

    // 等待时间配置（毫秒）
    waitTimeConfiguration: {
        baseWaitTimeMs: number;
        quickResponseTimeMs: number;
        minWaitTimeMs: number;
        maxWaitTimeMs: number;
        directMentionWaitTimeMs: number;
    };

    // 因子权重配置
    decisionFactorWeights: {
        topicAnalysisWeight: number;
        activityAnalysisWeight: number;
        paceAnalysisWeight: number;
        contextWeight: number;
    };

    // 规则引擎配置
    ruleEngine: {
        enableRuleBasedDecision: boolean;
        maxActiveRulesCount: number;
        ruleEvaluationTimeoutMs: number;
    };

    // 学习配置
    learningConfiguration: {
        enableFeedbackLearning: boolean;
        feedbackWeightDecay: number;
        maxFeedbackHistoryCount: number;
    };
}

// ==================== 智能化服务接口 ====================

/**
 * 语义分析结果
 */
export interface SemanticAnalysis {
    intent: string;
    entities: Entity[];
    sentiment: SentimentScore;
    topics: string[];
    keywords: string[];
    embedding?: number[];
    confidence: number;
}

/**
 * 实体
 */
export interface Entity {
    text: string;
    type: string;
    confidence: number;
    startIndex: number;
    endIndex: number;
}

/**
 * 情感分数
 */
export interface SentimentScore {
    positive: number;
    negative: number;
    neutral: number;
    overall: 'positive' | 'negative' | 'neutral';
}

/**
 * LLM配置
 */
export interface LLMConfig {
    provider: 'openai' | 'local' | 'adapter';
    model: string;
    apiKey?: string;
    endpoint?: string;
    maxTokens: number;
    temperature: number;
    timeout: number;
}

/**
 * NLP配置
 */
export interface NLPConfig {
    language: 'zh' | 'en' | 'auto';
    enableSegmentation: boolean;
    enablePOS: boolean;
    enableNER: boolean;
    enableSentiment: boolean;
}

/**
 * LLM提供商接口
 */
export interface ILLMProvider {
    name: string;

    /**
     * 语义分析
     */
    analyze(text: string, context?: Record<string, any>): Promise<SemanticAnalysis>;

    /**
     * 文本嵌入
     */
    embedText(text: string): Promise<number[]>;

    /**
     * 批量嵌入
     */
    embedTexts(texts: string[]): Promise<number[][]>;

    /**
     * 检查可用性
     */
    isAvailable(): Promise<boolean>;

    /**
     * 获取模型信息
     */
    getModelInfo(): ModelInfo;
}

/**
 * 模型信息
 */
export interface ModelInfo {
    name: string;
    version: string;
    capabilities: string[];
    maxInputLength: number;
    embeddingDimension?: number;
}

/**
 * NLP处理器接口
 */
export interface INLPProcessor {
    /**
     * 文本分词
     */
    tokenize(text: string): string[];

    /**
     * 关键词提取
     */
    extractKeywords(text: string, limit?: number): string[];

    /**
     * 词性标注
     */
    posTag(text: string): Array<{ word: string; pos: string }>;

    /**
     * 命名实体识别
     */
    recognizeEntities(text: string): Entity[];

    /**
     * 情感分析
     */
    analyzeSentiment(text: string): SentimentScore;

    /**
     * 文本相似度
     */
    calculateSimilarity(text1: string, text2: string): number;
}

/**
 * 智能化服务接口
 */
export interface IIntelligenceService {
    /**
     * 初始化服务
     */
    initialize(config: IntelligenceConfig): Promise<void>;

    /**
     * 语义分析
     */
    analyzeSemantics(text: string, context?: AnalysisContext): Promise<SemanticAnalysis>;

    /**
     * 计算文本相似度
     */
    calculateSimilarity(text1: string, text2: string): Promise<number>;

    /**
     * 批量处理
     */
    batchProcess(texts: string[], operation: 'analyze' | 'embed'): Promise<any[]>;

    /**
     * 获取缓存统计
     */
    getCacheStats(): CacheStats;

    /**
     * 清理缓存
     */
    clearCache(): void;
}

/**
 * 智能化配置
 */
export interface IntelligenceConfig {
    llm: LLMConfig;
    nlp: NLPConfig;
    embedding: {
        model: string;
        dimension: number;
        cacheSize: number;
        batchSize: number;
    };
    cache: {
        enabled: boolean;
        maxSize: number;
        ttl: number;
    };
}

/**
 * 缓存统计
 */
export interface CacheStats {
    hitRate: number;
    missRate: number;
    totalRequests: number;
    cacheSize: number;
    memoryUsage: number;
}
