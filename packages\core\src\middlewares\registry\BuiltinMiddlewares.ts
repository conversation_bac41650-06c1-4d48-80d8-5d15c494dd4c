import { MiddlewareDefinition, createMiddlewareDefinitions, MiddlewareRegistry } from "../core";
import { DatabaseStorageMiddleware } from "../impl/DatabaseStorage";
import { ErrorHandlingMiddleware } from "../impl/ErrorHandling";
import { LLMProcessingMiddleware } from "../impl/LLMProcessing";
import { ReplyConditionMiddleware } from "../impl/ReplyCondition";
import { ResponseHandlingMiddleware } from "../impl/ResponseHandling";

/**
 * 内置中间件标识符
 */
export const BUILTIN_MIDDLEWARE_IDS = {
    ERROR_HANDLING: "builtin.error-handling",
    DATABASE_STORAGE: "builtin.database-storage",
    REPLY_CONDITION: "builtin.reply-condition",
    LLM_PROCESSING: "builtin.llm-processing",
    RESPONSE_HANDLING: "builtin.response-handling",
} as const;

/**
 * 创建内置中间件定义
 */
export function createBuiltinMiddlewares(): Map<string, MiddlewareDefinition> {
    return createMiddlewareDefinitions({
        [BUILTIN_MIDDLEWARE_IDS.ERROR_HANDLING]: {
            class: ErrorHandlingMiddleware,
            defaultConfig: {
                debug: false,
                uploadDump: false,
                pasteServiceUrl: "https://dump.yesimbot.chat/",
                includeFullSessionContent: false,
            },
        },
        [BUILTIN_MIDDLEWARE_IDS.DATABASE_STORAGE]: {
            class: DatabaseStorageMiddleware,
            defaultConfig: {
                enableImageProcessing: true,
                enableWorldStateUpdate: true,
                batchSize: 10,
                timeout: 5000,
            },
        },
        [BUILTIN_MIDDLEWARE_IDS.REPLY_CONDITION]: {
            class: ReplyConditionMiddleware,
            defaultConfig: {
                channels: [],
                testMode: false,
                strategies: {
                    atMention: { enabled: true, probability: 1.0 },
                    threshold: { enabled: true, value: 0.7 },
                    conversationFlow: { enabled: true, confidenceThreshold: 0.8 },
                },
                timing: {
                    waitTime: 1000,
                    sameUserThreshold: 5000,
                },
            },
        },
        [BUILTIN_MIDDLEWARE_IDS.LLM_PROCESSING]: {
            class: LLMProcessingMiddleware,
            defaultConfig: {
                debug: false,
                retryConfig: {
                    maxRetries: 3,
                    timeoutMs: 30000,
                    retryDelayMs: 1000,
                    exponentialBackoff: true,
                    retryableErrors: ["ECONNREFUSED", "ECONNRESET", "ETIMEDOUT"],
                },
                adapterSwitchingConfig: {
                    enabled: true,
                    maxAttempts: 3,
                },
                timeout: 60000,
                enableStreaming: true,
            },
        },
        [BUILTIN_MIDDLEWARE_IDS.RESPONSE_HANDLING]: {
            class: ResponseHandlingMiddleware,
            defaultConfig: {
                maxRetry: 3,
                life: 3,
                maxHeartbeat: 2,
                enableToolValidation: true,
                parallelExecution: false,
            },
        },
    });
}

/**
 * 注册所有内置中间件到注册表
 */
export function registerBuiltinMiddlewares(registry: MiddlewareRegistry): void {
    const definitions = createBuiltinMiddlewares();

    for (const [id, definition] of definitions) {
        registry.register(id, definition);
    }
}

/**
 * URL验证辅助函数
 */
function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}
