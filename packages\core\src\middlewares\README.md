# 简化中间件架构

## 概述

这是一个简化的中间件架构，专注于核心业务流程，移除了过度复杂的功能。

## 核心设计原则

1. **专注核心业务流程**：收集消息 → 判断回复条件 → 构造LLM请求 → 解析执行响应
2. **保留心跳机制**：支持AI连续工具调用
3. **移除过度抽象**：减少不必要的管理器和抽象层
4. **简化状态管理**：只保留必要的执行状态

## 架构组件

### SimpleMiddlewareManager
- 替代复杂的 EnhancedMiddlewareManager
- 只保留基本的中间件链执行
- 支持从指定位置重启（心跳机制）

### SimpleMiddleware 接口
```typescript
interface SimpleMiddleware {
    readonly id: string;
    readonly name: string;
    readonly enabled: boolean;
    execute(ctx: MessageContext, next: () => Promise<void>): Promise<void>;
    initialize?(): Promise<void>;
    dispose?(): Promise<void>;
}
```

### 核心中间件链
1. **ErrorHandling** - 错误处理
2. **DatabaseStorage** - 消息存储
3. **ReplyCondition** - 回复条件判断
4. **LLMProcessing** - LLM处理
5. **ResponseHandling** - 响应处理+心跳

## 心跳机制

当AI请求心跳时（`request_heartbeat: true`），系统会：

1. 增加心跳计数器
2. 检查是否超过最大心跳次数
3. 调用 `SimpleMiddlewareManager.restartFromLLM()`
4. 从 LLMProcessing 中间件重新开始执行

## 使用方式

```typescript
import { SimpleMiddlewareConfigurator } from "./simple";

// 创建配置器
const configurator = new SimpleMiddlewareConfigurator(ctx, config, services);

// 配置中间件管理器
const manager = await configurator.configure();

// 执行中间件链
await manager.execute(messageContext);
```

## 与旧架构的对比

### 移除的复杂功能
- ❌ 执行状态快照和恢复
- ❌ 复杂的事件驱动系统
- ❌ 多响应队列管理
- ❌ 执行断点和跳转
- ❌ 细粒度状态管理

### 保留的核心功能
- ✅ 基本中间件链执行
- ✅ 心跳重启机制
- ✅ 错误处理
- ✅ 简单的调试支持

## 配置选项

```typescript
interface SimpleMiddlewareOptions {
    maxHeartbeat?: number;  // 最大心跳次数，默认 3
    debug?: boolean;        // 调试模式，默认 false
}
```

## 迁移指南

从旧的增强中间件架构迁移到简化架构：

1. 将 `EnhancedMiddlewareManager` 替换为 `SimpleMiddlewareManager`
2. 将 `MiddlewareConfigurator` 替换为 `SimpleMiddlewareConfigurator`
3. 移除对事件系统和多响应管理的依赖
4. 简化中间件实现，移除复杂的状态管理

## 性能优势

- 🚀 更少的内存占用
- 🚀 更快的执行速度
- 🚀 更简单的调试过程
- 🚀 更容易的维护和扩展
