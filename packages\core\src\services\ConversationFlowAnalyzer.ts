import { ChatMessage } from "../shared/database";
import { Context } from "koishi";

// 对话流分析器配置接口
export interface ConversationFlowConfig {
    // 消息分析配置
    messageAnalysis: {
        maxRecentMessages: number;           // 最大保留的最近消息数
        analysisWindowSize: number;          // 分析窗口大小（消息数）
        keywordExtractionLimit: number;      // 关键词提取数量限制
        topicKeywordLimit: number;           // 话题关键词数量限制
    };

    // 时间阈值配置
    timeThresholds: {
        topicTimeoutMs: number;              // 话题超时时间（毫秒）
        topicCoolingMs: number;              // 话题冷却时间（毫秒）
        fastPaceThresholdMs: number;         // 快节奏阈值（毫秒）
        slowPaceThresholdMs: number;         // 慢节奏阈值（毫秒）
        paceAnalysisWindowMs: number;        // 节奏分析时间窗口（毫秒）
    };

    // 置信度阈值配置
    confidenceThresholds: {
        replyDecisionThreshold: number;      // 回复决策阈值
        topicContinuationThreshold: number;  // 话题延续阈值
        responseToMessageThreshold: number;  // 消息回应阈值
        noActiveTopicsConfidence: number;    // 无活跃话题置信度
        stableTopicsConfidence: number;      // 稳定话题置信度
        matureTopicsConfidence: number;      // 成熟话题置信度
        developingTopicsConfidence: number;  // 发展中话题置信度
        slowConversationConfidence: number;  // 慢对话置信度
        fastConversationConfidence: number;  // 快对话置信度
        normalPaceConfidence: number;        // 正常节奏置信度
        insufficientDataConfidence: number;  // 数据不足置信度
    };

    // 话题分析配置
    topicAnalysis: {
        minMessagesForMature: number;        // 成熟话题最小消息数
        minParticipantsForStable: number;    // 稳定话题最小参与者数
        minMessagesForStable: number;        // 稳定话题最小消息数
        initialTopicStability: number;       // 新话题初始稳定性
    };

    // 等待时间配置
    waitTimeConfig: {
        baseWaitTimeMs: number;              // 基础等待时间
        quickResponseMs: number;             // 快速响应时间
        minWaitTimeMs: number;               // 最小等待时间
        maxWaitTimeMs: number;               // 最大等待时间
        developingTopicMultiplier: number;   // 发展中话题等待时间倍数
        stableTopicMultiplier: number;       // 稳定话题等待时间倍数
        noActiveTopicMultiplier: number;     // 无活跃话题等待时间倍数
        fastPaceMultiplier: number;          // 快节奏等待时间倍数
        slowPaceMultiplier: number;          // 慢节奏等待时间倍数
    };

    // 节奏分析配置
    paceAnalysis: {
        minMessagesForAnalysis: number;      // 节奏分析最小消息数
        recentMessagesForPace: number;       // 节奏分析使用的最近消息数
        paceCalculationWindow: number;       // 节奏计算窗口大小
    };

    // 状态管理配置
    stateManagement: {
        maxActiveTopics: number;             // 最大活跃话题数
        maxUserActivities: number;           // 最大用户活跃度记录数
        memoryCleanupIntervalMs: number;     // 内存清理间隔
        topicMergeThreshold: number;         // 话题合并阈值
        userActivityTimeoutMs: number;       // 用户活跃度超时时间
        enablePerformanceOptimization: boolean; // 启用性能优化
        enableMemoryManagement: boolean;     // 启用内存管理
    };

    // 关键词和模式配置
    patterns: {
        stopWords: string[];                 // 停用词列表
        replyKeywords: string[];             // 回复关键词
        mentionKeywords: string[];           // 提及关键词
        questionPatterns: RegExp[];          // 问题模式
    };
}

// 默认配置
export const DEFAULT_CONVERSATION_FLOW_CONFIG: ConversationFlowConfig = {
    messageAnalysis: {
        maxRecentMessages: 15,
        analysisWindowSize: 8,
        keywordExtractionLimit: 5,
        topicKeywordLimit: 10,
    },

    timeThresholds: {
        topicTimeoutMs: 10 * 60 * 1000,      // 10分钟
        topicCoolingMs: 5 * 60 * 1000,       // 5分钟
        fastPaceThresholdMs: 5000,           // 5秒
        slowPaceThresholdMs: 30000,          // 30秒
        paceAnalysisWindowMs: 60000,         // 1分钟
    },

    confidenceThresholds: {
        replyDecisionThreshold: 0.6,
        topicContinuationThreshold: 0.7,
        responseToMessageThreshold: 0.8,
        noActiveTopicsConfidence: 0.8,
        stableTopicsConfidence: 0.7,
        matureTopicsConfidence: 0.6,
        developingTopicsConfidence: 0.2,
        slowConversationConfidence: 0.8,
        fastConversationConfidence: 0.2,
        normalPaceConfidence: 0.5,
        insufficientDataConfidence: 0.5,
    },

    topicAnalysis: {
        minMessagesForMature: 3,
        minParticipantsForStable: 2,
        minMessagesForStable: 5,
        initialTopicStability: 0.1,
    },

    waitTimeConfig: {
        baseWaitTimeMs: 3000,                // 3秒
        quickResponseMs: 1000,               // 1秒
        minWaitTimeMs: 1000,                 // 1秒
        maxWaitTimeMs: 8000,                 // 8秒
        developingTopicMultiplier: 2.0,
        stableTopicMultiplier: 0.8,
        noActiveTopicMultiplier: 0.6,
        fastPaceMultiplier: 2.5,
        slowPaceMultiplier: 0.5,
    },

    paceAnalysis: {
        minMessagesForAnalysis: 2,
        recentMessagesForPace: 5,
        paceCalculationWindow: 6,
    },

    stateManagement: {
        maxActiveTopics: 5,                  // 最大活跃话题数
        maxUserActivities: 50,               // 最大用户活跃度记录数
        memoryCleanupIntervalMs: 5 * 60 * 1000, // 5分钟清理间隔
        topicMergeThreshold: 0.8,            // 话题合并阈值
        userActivityTimeoutMs: 30 * 60 * 1000, // 30分钟用户活跃度超时
        enablePerformanceOptimization: true, // 启用性能优化
        enableMemoryManagement: true,        // 启用内存管理
    },

    patterns: {
        stopWords: [
            // 中文停用词
            "的", "了", "是", "在", "我", "你", "他", "她", "它", "这", "那",
            "有", "和", "与", "就", "都", "要", "会", "能", "可以", "不是",
            // 英文停用词
            "the", "a", "an", "and", "or", "but", "in", "on", "at", "to",
            "for", "of", "with", "by", "is", "are", "was", "were", "be"
        ],
        replyKeywords: ["回复", "回应", "@", "刚才", "上面", "之前", "刚刚"],
        mentionKeywords: ["@", "机器人", "bot", "AI", "助手"],
        questionPatterns: [/[？?]/, /^(什么|怎么|为什么|哪里|谁|何时)/],
    },
};

// 消息关联类型
export type MessageRelationType =
    | "topic_continuation" // 话题延续
    | "topic_shift" // 话题转移
    | "response_to_previous" // 回应之前的消息
    | "new_topic" // 全新话题
    | "side_conversation"; // 旁支对话

// 话题状态
export type TopicStatus =
    | "developing" // 正在发展
    | "stable" // 稳定讨论
    | "cooling" // 逐渐冷却
    | "ended"; // 已结束

// 消息分析结果
export interface MessageAnalysis {
    messageId: string;
    relationType: MessageRelationType;
    topicId?: string;
    referencedMessageIds: string[];
    confidence: number; // 0-1，分析的置信度
    timestamp: Date;
}

// 用户活跃度分析
export interface UserActivity {
    userId: string;
    messageCount: number;
    lastMessageTime: Date;
    averageInterval: number;        // 平均发言间隔
    participationScore: number;     // 参与度评分 0-1
    isActiveParticipant: boolean;   // 是否为活跃参与者
}

// 话题分析结果
export interface TopicAnalysis {
    topicId: string;
    status: TopicStatus;
    participants: Set<string>;
    lastActivity: Date;
    messageCount: number;
    keywords: string[];
    stability: number;              // 话题稳定性 0-1
    dominantParticipants: string[]; // 主要参与者
    engagementLevel: number;        // 参与度水平 0-1
    topicCoherence: number;         // 话题连贯性 0-1
}

// 对话流状态
export interface ConversationFlow {
    activeTopics: Map<string, TopicAnalysis>;
    recentMessages: MessageAnalysis[];
    conversationPace: "fast" | "normal" | "slow";
    lastAnalysisTime: Date;
    userActivities: Map<string, UserActivity>;    // 用户活跃度跟踪
    groupDynamics: {
        totalParticipants: number;                // 总参与者数
        activeParticipants: number;               // 活跃参与者数
        conversationIntensity: number;            // 对话强度 0-1
        topicSwitchFrequency: number;             // 话题切换频率
    };
}

// 回复决策结果
interface ReplyDecision {
    shouldReply: boolean;
    reason: string;
    confidence: number;
    suggestedWaitTime?: number;
}

export class ConversationFlowAnalyzer {
    private flows = new Map<string, ConversationFlow>();
    private config: ConversationFlowConfig;

    constructor(
        private ctx: Context,
        config?: Partial<ConversationFlowConfig>
    ) {
        // 合并默认配置和用户配置
        this.config = this.mergeConfig(DEFAULT_CONVERSATION_FLOW_CONFIG, config || {});
    }

    /**
     * 合并配置
     */
    private mergeConfig(
        defaultConfig: ConversationFlowConfig,
        userConfig: Partial<ConversationFlowConfig>
    ): ConversationFlowConfig {
        return {
            messageAnalysis: { ...defaultConfig.messageAnalysis, ...userConfig.messageAnalysis },
            timeThresholds: { ...defaultConfig.timeThresholds, ...userConfig.timeThresholds },
            confidenceThresholds: { ...defaultConfig.confidenceThresholds, ...userConfig.confidenceThresholds },
            topicAnalysis: { ...defaultConfig.topicAnalysis, ...userConfig.topicAnalysis },
            waitTimeConfig: { ...defaultConfig.waitTimeConfig, ...userConfig.waitTimeConfig },
            paceAnalysis: { ...defaultConfig.paceAnalysis, ...userConfig.paceAnalysis },
            stateManagement: { ...defaultConfig.stateManagement, ...userConfig.stateManagement },
            patterns: { ...defaultConfig.patterns, ...userConfig.patterns },
        };
    }

    /**
     * 更新配置
     */
    public updateConfig(newConfig: Partial<ConversationFlowConfig>): void {
        this.config = this.mergeConfig(this.config, newConfig);
    }

    /**
     * 获取当前配置
     */
    public getConfig(): ConversationFlowConfig {
        return { ...this.config };
    }

    /**
     * 分析新消息并更新对话流
     */
    public async analyzeMessage(channelId: string, message: ChatMessage): Promise<MessageAnalysis> {
        let flow = this.flows.get(channelId);
        if (!flow) {
            flow = {
                activeTopics: new Map(),
                recentMessages: [],
                conversationPace: "normal",
                lastAnalysisTime: new Date(),
                userActivities: new Map(),
                groupDynamics: {
                    totalParticipants: 0,
                    activeParticipants: 0,
                    conversationIntensity: 0,
                    topicSwitchFrequency: 0,
                },
            };
            this.flows.set(channelId, flow);
        }

        // 分析消息关联性
        const analysis = await this.analyzeMessageRelation(message, flow);

        // 更新对话流状态
        this.updateConversationFlow(flow, analysis, message);

        // 更新用户活跃度
        this.updateUserActivity(flow, message);

        // 更新群组动态
        this.updateGroupDynamics(flow);

        // 清理过期话题
        this.cleanupExpiredTopics(flow);

        // 状态管理优化
        if (this.config.stateManagement.enableMemoryManagement) {
            this.performMemoryManagement(flow);
        }

        return analysis;
    }

    /**
     * 判断是否适合回复
     */
    public shouldReply(channelId: string, currentMessage: ChatMessage): ReplyDecision {
        const flow = this.flows.get(channelId);
        if (!flow) {
            return { shouldReply: false, reason: "no_flow_data", confidence: 0 };
        }

        // 如果被@，立即回复
        if (this.isDirectMention(currentMessage)) {
            return {
                shouldReply: true,
                reason: "direct_mention",
                confidence: 1.0,
                suggestedWaitTime: this.config.waitTimeConfig.quickResponseMs,
            };
        }

        // 分析话题状态
        const topicAnalysis = this.analyzeTopicReadiness(flow);

        // 分析对话节奏
        const paceAnalysis = this.analyzePaceReadiness(flow);

        // 综合判断
        const confidence = (topicAnalysis.confidence + paceAnalysis.confidence) / 2;
        const shouldReply = confidence > this.config.confidenceThresholds.replyDecisionThreshold;

        // 计算建议等待时间
        const suggestedWaitTime = this.calculateSuggestedWaitTime(flow, topicAnalysis, paceAnalysis);

        return {
            shouldReply,
            reason: shouldReply ? topicAnalysis.reason : "topic_still_developing",
            confidence,
            suggestedWaitTime,
        };
    }

    /**
     * 分析消息关联性
     */
    private async analyzeMessageRelation(message: ChatMessage, flow: ConversationFlow): Promise<MessageAnalysis> {
        const recentMessages = flow.recentMessages.slice(-this.config.messageAnalysis.analysisWindowSize);

        // 提取关键词
        const keywords = this.extractKeywords(message.content as string);
        let bestMatch: { topicId?: string; confidence: number; type: MessageRelationType } = {
            confidence: 0,
            type: "new_topic",
        };

        // 检查是否与现有话题相关
        for (const [topicId, topic] of flow.activeTopics) {
            const similarity = this.calculateTopicSimilarity(keywords, topic.keywords);
            if (similarity > bestMatch.confidence) {
                bestMatch = {
                    topicId,
                    confidence: similarity,
                    type: similarity > this.config.confidenceThresholds.topicContinuationThreshold
                        ? "topic_continuation"
                        : "topic_shift",
                };
            }
        }

        // 检查是否回应之前的消息
        const referencedMessages = this.findReferencedMessages(message, recentMessages);
        if (referencedMessages.length > 0 && bestMatch.confidence < this.config.confidenceThresholds.responseToMessageThreshold) {
            bestMatch = {
                confidence: this.config.confidenceThresholds.responseToMessageThreshold,
                type: "response_to_previous",
            };
        }

        return {
            messageId: message.messageId,
            relationType: bestMatch.type,
            topicId: bestMatch.topicId,
            referencedMessageIds: referencedMessages,
            confidence: bestMatch.confidence,
            timestamp: message.timestamp,
        };
    }

    /**
     * 更新对话流状态
     */
    private updateConversationFlow(flow: ConversationFlow, analysis: MessageAnalysis, message: ChatMessage): void {
        // 添加到最近消息
        flow.recentMessages.push(analysis);
        if (flow.recentMessages.length > this.config.messageAnalysis.maxRecentMessages) {
            flow.recentMessages.shift();
        }

        // 更新话题状态
        if (analysis.topicId) {
            const topic = flow.activeTopics.get(analysis.topicId);
            if (topic) {
                topic.lastActivity = analysis.timestamp;
                topic.messageCount++;
                topic.participants.add(message.sender.id);
                topic.status = this.determineTopicStatus(topic, flow.recentMessages);

                // 更新关键词
                const newKeywords = this.extractKeywords(message.content as string);
                topic.keywords = [...new Set([...topic.keywords, ...newKeywords])]
                    .slice(0, this.config.messageAnalysis.topicKeywordLimit);

                // 更新主要参与者
                this.updateTopicDominantParticipants(topic, message.sender.id);

                // 更新参与度水平
                topic.engagementLevel = this.calculateTopicEngagement(topic, flow);

                // 更新话题连贯性
                topic.topicCoherence = this.calculateTopicCoherence(topic, analysis, flow);
            }
        } else if (analysis.relationType === "new_topic") {
            // 创建新话题
            const newTopicId = `topic_${Date.now()}_${message.sender.id}`;
            const keywords = this.extractKeywords(message.content as string);

            flow.activeTopics.set(newTopicId, {
                topicId: newTopicId,
                status: "developing",
                participants: new Set([message.sender.id]),
                lastActivity: analysis.timestamp,
                messageCount: 1,
                keywords: keywords,
                stability: this.config.topicAnalysis.initialTopicStability,
                dominantParticipants: [message.sender.id],
                engagementLevel: 0.1,
                topicCoherence: 1.0,
            });
        }

        // 更新对话节奏
        flow.conversationPace = this.calculateConversationPace(flow.recentMessages);
        flow.lastAnalysisTime = new Date();
    }

    /**
     * 分析话题准备状态（增强群聊逻辑）
     */
    private analyzeTopicReadiness(flow: ConversationFlow): { confidence: number; reason: string } {
        const activeTopics = Array.from(flow.activeTopics.values());

        // 如果没有活跃话题，可以回复
        if (activeTopics.length === 0) {
            return {
                confidence: this.config.confidenceThresholds.noActiveTopicsConfidence,
                reason: "no_active_topics"
            };
        }

        // 考虑群组动态
        const groupDynamics = flow.groupDynamics;
        let baseConfidence = this.config.confidenceThresholds.developingTopicsConfidence;
        let reason = "topics_developing";

        // 检查话题是否稳定或冷却
        const stableTopics = activeTopics.filter((t) => t.status === "stable" || t.status === "cooling");
        if (stableTopics.length > 0) {
            baseConfidence = this.config.confidenceThresholds.stableTopicsConfidence;
            reason = "topics_stable";
        }

        // 检查话题是否已经有足够的讨论
        const matureTopics = activeTopics.filter((t) => t.messageCount >= this.config.topicAnalysis.minMessagesForMature);
        if (matureTopics.length > 0 && baseConfidence < this.config.confidenceThresholds.matureTopicsConfidence) {
            baseConfidence = this.config.confidenceThresholds.matureTopicsConfidence;
            reason = "topics_mature";
        }

        // 群聊特有的调整因子
        let adjustmentFactor = 1.0;

        // 如果对话强度很高，降低回复倾向
        if (groupDynamics.conversationIntensity > 0.8) {
            adjustmentFactor *= 0.7;
            reason += "_high_intensity";
        }

        // 如果话题切换频繁，等待话题稳定
        if (groupDynamics.topicSwitchFrequency > 0.5) {
            adjustmentFactor *= 0.6;
            reason += "_frequent_switches";
        }

        // 如果活跃参与者很少，可以适当提高回复倾向
        if (groupDynamics.activeParticipants <= 2 && groupDynamics.totalParticipants > 3) {
            adjustmentFactor *= 1.2;
            reason += "_low_participation";
        }

        // 考虑话题连贯性
        const avgCoherence = activeTopics.reduce((sum, topic) => sum + topic.topicCoherence, 0) / activeTopics.length;
        if (avgCoherence < 0.5) {
            adjustmentFactor *= 0.8;
            reason += "_low_coherence";
        }

        const finalConfidence = Math.min(1, Math.max(0, baseConfidence * adjustmentFactor));

        return { confidence: finalConfidence, reason };
    }

    /**
     * 分析节奏准备状态（增强算法）
     */
    private analyzePaceReadiness(flow: ConversationFlow): { confidence: number; reason: string } {
        const recentMessages = flow.recentMessages.slice(-this.config.paceAnalysis.recentMessagesForPace);
        if (recentMessages.length < this.config.paceAnalysis.minMessagesForAnalysis) {
            return {
                confidence: this.config.confidenceThresholds.insufficientDataConfidence,
                reason: "insufficient_data"
            };
        }

        // 多时间窗口分析
        const paceAnalysis = this.analyzeMultiWindowPace(recentMessages, flow);

        // 用户参与度影响
        const participationFactor = this.calculateParticipationFactor(flow);

        // 动态阈值调整
        const dynamicThresholds = this.calculateDynamicThresholds(flow);

        // 综合评估
        return this.evaluatePaceReadiness(paceAnalysis, participationFactor, dynamicThresholds);
    }

    /**
     * 多时间窗口节奏分析
     */
    private analyzeMultiWindowPace(messages: MessageAnalysis[], flow: ConversationFlow): {
        shortTermPace: number;
        mediumTermPace: number;
        longTermPace: number;
        paceVariability: number;
        burstDetection: boolean;
    } {
        const now = Date.now();

        // 短期窗口（最近2分钟）
        const shortTermWindow = 2 * 60 * 1000;
        const shortTermMessages = messages.filter(m => now - m.timestamp.getTime() < shortTermWindow);
        const shortTermPace = this.calculateWindowPace(shortTermMessages);

        // 中期窗口（最近5分钟）
        const mediumTermWindow = 5 * 60 * 1000;
        const mediumTermMessages = messages.filter(m => now - m.timestamp.getTime() < mediumTermWindow);
        const mediumTermPace = this.calculateWindowPace(mediumTermMessages);

        // 长期窗口（最近10分钟）
        const longTermWindow = 10 * 60 * 1000;
        const longTermMessages = messages.filter(m => now - m.timestamp.getTime() < longTermWindow);
        const longTermPace = this.calculateWindowPace(longTermMessages);

        // 计算节奏变化率
        const paceVariability = this.calculatePaceVariability([shortTermPace, mediumTermPace, longTermPace]);

        // 检测消息爆发
        const burstDetection = this.detectMessageBurst(shortTermMessages);

        return {
            shortTermPace,
            mediumTermPace,
            longTermPace,
            paceVariability,
            burstDetection
        };
    }

    /**
     * 计算时间窗口内的节奏
     */
    private calculateWindowPace(messages: MessageAnalysis[]): number {
        if (messages.length < 2) return 0;

        const intervals = [];
        for (let i = 1; i < messages.length; i++) {
            const interval = messages[i].timestamp.getTime() - messages[i - 1].timestamp.getTime();
            intervals.push(interval);
        }

        return intervals.reduce((a, b) => a + b, 0) / intervals.length;
    }

    /**
     * 计算节奏变化率
     */
    private calculatePaceVariability(paces: number[]): number {
        if (paces.length < 2) return 0;

        const validPaces = paces.filter(p => p > 0);
        if (validPaces.length < 2) return 0;

        const mean = validPaces.reduce((a, b) => a + b, 0) / validPaces.length;
        const variance = validPaces.reduce((sum, pace) => sum + Math.pow(pace - mean, 2), 0) / validPaces.length;

        return Math.sqrt(variance) / mean; // 变异系数
    }

    /**
     * 检测消息爆发
     */
    private detectMessageBurst(messages: MessageAnalysis[]): boolean {
        if (messages.length < 3) return false;

        const burstThreshold = 3; // 3条消息
        const burstTimeWindow = 30 * 1000; // 30秒内

        const now = Date.now();
        const recentMessages = messages.filter(m => now - m.timestamp.getTime() < burstTimeWindow);

        return recentMessages.length >= burstThreshold;
    }

    /**
     * 计算参与度因子
     */
    private calculateParticipationFactor(flow: ConversationFlow): number {
        const groupDynamics = flow.groupDynamics;

        // 活跃参与者比例
        const participationRatio = groupDynamics.totalParticipants > 0 ?
            groupDynamics.activeParticipants / groupDynamics.totalParticipants : 0;

        // 对话强度
        const intensity = groupDynamics.conversationIntensity;

        // 综合参与度因子
        return participationRatio * 0.6 + intensity * 0.4;
    }

    /**
     * 计算动态阈值
     */
    private calculateDynamicThresholds(flow: ConversationFlow): {
        fastThreshold: number;
        slowThreshold: number;
    } {
        const baseFast = this.config.timeThresholds.fastPaceThresholdMs;
        const baseSlow = this.config.timeThresholds.slowPaceThresholdMs;

        // 根据群组规模调整
        const groupSize = flow.groupDynamics.totalParticipants;
        const sizeMultiplier = Math.max(0.5, Math.min(2.0, 1 + (groupSize - 3) * 0.1));

        // 根据话题切换频率调整
        const switchFrequency = flow.groupDynamics.topicSwitchFrequency;
        const switchMultiplier = 1 + switchFrequency * 0.5;

        return {
            fastThreshold: baseFast * sizeMultiplier * switchMultiplier,
            slowThreshold: baseSlow * sizeMultiplier
        };
    }

    /**
     * 评估节奏准备状态
     */
    private evaluatePaceReadiness(
        paceAnalysis: any,
        participationFactor: number,
        dynamicThresholds: any
    ): { confidence: number; reason: string } {
        let confidence = this.config.confidenceThresholds.normalPaceConfidence;
        let reason = "normal_pace";

        // 检测消息爆发
        if (paceAnalysis.burstDetection) {
            confidence = this.config.confidenceThresholds.fastConversationConfidence * 0.5;
            reason = "message_burst_detected";
            return { confidence, reason };
        }

        // 短期节奏分析
        if (paceAnalysis.shortTermPace > 0) {
            if (paceAnalysis.shortTermPace < dynamicThresholds.fastThreshold) {
                confidence = this.config.confidenceThresholds.fastConversationConfidence;
                reason = "conversation_too_fast";
            } else if (paceAnalysis.shortTermPace > dynamicThresholds.slowThreshold) {
                confidence = this.config.confidenceThresholds.slowConversationConfidence;
                reason = "conversation_slowing";
            }
        }

        // 考虑节奏变化率
        if (paceAnalysis.paceVariability > 1.0) {
            confidence *= 0.8; // 节奏不稳定，降低置信度
            reason += "_unstable_pace";
        }

        // 考虑参与度
        if (participationFactor < 0.3) {
            confidence *= 1.2; // 参与度低，提高回复倾向
            reason += "_low_participation";
        } else if (participationFactor > 0.8) {
            confidence *= 0.9; // 参与度高，稍微降低回复倾向
            reason += "_high_participation";
        }

        // 中长期趋势分析
        if (paceAnalysis.mediumTermPace > paceAnalysis.longTermPace * 1.5) {
            confidence *= 0.8; // 节奏在加快，等待
            reason += "_accelerating";
        } else if (paceAnalysis.mediumTermPace < paceAnalysis.longTermPace * 0.7) {
            confidence *= 1.1; // 节奏在放缓，可以参与
            reason += "_decelerating";
        }

        return {
            confidence: Math.min(1, Math.max(0, confidence)),
            reason
        };
    }

    /**
     * 计算建议等待时间
     */
    private calculateSuggestedWaitTime(
        flow: ConversationFlow,
        topicAnalysis: { confidence: number; reason: string },
        paceAnalysis: { confidence: number; reason: string }
    ): number {
        let baseWaitTime = this.config.waitTimeConfig.baseWaitTimeMs;

        // 根据话题状态调整
        switch (topicAnalysis.reason) {
            case "topics_developing":
                baseWaitTime *= this.config.waitTimeConfig.developingTopicMultiplier;
                break;
            case "topics_stable":
                baseWaitTime *= this.config.waitTimeConfig.stableTopicMultiplier;
                break;
            case "no_active_topics":
                baseWaitTime *= this.config.waitTimeConfig.noActiveTopicMultiplier;
                break;
        }

        // 根据节奏调整
        switch (paceAnalysis.reason) {
            case "conversation_too_fast":
                baseWaitTime *= this.config.waitTimeConfig.fastPaceMultiplier;
                break;
            case "conversation_slowing":
                baseWaitTime *= this.config.waitTimeConfig.slowPaceMultiplier;
                break;
        }

        return Math.max(
            this.config.waitTimeConfig.minWaitTimeMs,
            Math.min(baseWaitTime, this.config.waitTimeConfig.maxWaitTimeMs)
        );
    }

    /**
     * 提取关键词
     */
    private extractKeywords(content: string): string[] {
        // 简化的关键词提取
        const cleanContent = content
            .toLowerCase()
            .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, " ")
            .replace(/\s+/g, " ")
            .trim();

        const words = cleanContent
            .split(" ")
            .filter((word) => word.length > 1)
            .filter((word) => !this.isStopWord(word));

        return [...new Set(words)].slice(0, this.config.messageAnalysis.keywordExtractionLimit);
    }

    /**
     * 判断是否为停用词
     */
    private isStopWord(word: string): boolean {
        const stopWords = new Set(this.config.patterns.stopWords);
        return stopWords.has(word);
    }

    /**
     * 计算话题相似度
     */
    private calculateTopicSimilarity(keywords1: string[], keywords2: string[]): number {
        if (keywords1.length === 0 || keywords2.length === 0) return 0;

        const set1 = new Set(keywords1);
        const set2 = new Set(keywords2);
        const intersection = new Set([...set1].filter((k) => set2.has(k)));
        const union = new Set([...set1, ...set2]);

        return intersection.size / union.size;
    }

    /**
     * 查找引用的消息
     */
    private findReferencedMessages(message: ChatMessage, recentMessages: MessageAnalysis[]): string[] {
        const content = message.content as string;
        const replyKeywords = this.config.patterns.replyKeywords;

        if (replyKeywords.some((keyword) => content.includes(keyword))) {
            return recentMessages.slice(-3).map((m) => m.messageId);
        }

        return [];
    }

    /**
     * 确定话题状态
     */
    private determineTopicStatus(topic: TopicAnalysis, recentMessages: MessageAnalysis[]): TopicStatus {
        const now = Date.now();
        const timeSinceLastActivity = now - topic.lastActivity.getTime();

        // 超过话题超时时间，话题结束
        if (timeSinceLastActivity > this.config.timeThresholds.topicTimeoutMs) {
            return "ended";
        }

        // 超过冷却时间没有相关消息，话题冷却
        if (timeSinceLastActivity > this.config.timeThresholds.topicCoolingMs) {
            return "cooling";
        }

        // 根据消息数量和参与者判断
        if (topic.messageCount >= this.config.topicAnalysis.minMessagesForStable &&
            topic.participants.size >= this.config.topicAnalysis.minParticipantsForStable) {
            return "stable";
        }

        return "developing";
    }

    /**
     * 计算对话节奏
     */
    private calculateConversationPace(recentMessages: MessageAnalysis[]): "fast" | "normal" | "slow" {
        if (recentMessages.length < 3) return "normal";

        const intervals = [];
        for (let i = 1; i < Math.min(recentMessages.length, this.config.paceAnalysis.paceCalculationWindow); i++) {
            const interval = recentMessages[i].timestamp.getTime() - recentMessages[i - 1].timestamp.getTime();
            intervals.push(interval);
        }

        const avgInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;

        if (avgInterval < this.config.timeThresholds.fastPaceThresholdMs * 2) return "fast"; // 快节奏阈值的2倍
        if (avgInterval > this.config.timeThresholds.slowPaceThresholdMs * 2) return "slow"; // 慢节奏阈值的2倍
        return "normal";
    }

    /**
     * 检查是否直接提及
     */
    private isDirectMention(message: ChatMessage): boolean {
        const content = message.content as string;
        const mentionKeywords = this.config.patterns.mentionKeywords;

        return mentionKeywords.some(keyword => content.includes(keyword));
    }

    /**
     * 清理过期话题
     */
    private cleanupExpiredTopics(flow: ConversationFlow): void {
        const now = Date.now();
        for (const [topicId, topic] of flow.activeTopics) {
            if (now - topic.lastActivity.getTime() > this.config.timeThresholds.topicTimeoutMs) {
                flow.activeTopics.delete(topicId);
            }
        }
    }

    /**
     * 获取对话流状态
     */
    public getConversationFlow(channelId: string): ConversationFlow | null {
        return this.flows.get(channelId) || null;
    }

    /**
     * 更新话题主要参与者
     */
    private updateTopicDominantParticipants(topic: TopicAnalysis, userId: string): void {
        // 统计每个参与者的消息数量
        const participantCounts = new Map<string, number>();

        // 初始化计数
        for (const participant of topic.participants) {
            participantCounts.set(participant, 0);
        }

        // 这里简化处理，实际应该基于消息历史统计
        // 当前参与者消息数+1
        const currentCount = participantCounts.get(userId) || 0;
        participantCounts.set(userId, currentCount + 1);

        // 按消息数排序，取前3名作为主要参与者
        const sortedParticipants = Array.from(participantCounts.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 3)
            .map(([id]) => id);

        topic.dominantParticipants = sortedParticipants;
    }

    /**
     * 计算话题参与度
     */
    private calculateTopicEngagement(topic: TopicAnalysis, flow: ConversationFlow): number {
        const recentTimeWindow = 5 * 60 * 1000; // 5分钟窗口
        const now = Date.now();

        // 时间衰减因子
        const timeSinceLastActivity = now - topic.lastActivity.getTime();
        const timeDecay = Math.max(0, 1 - timeSinceLastActivity / recentTimeWindow);

        // 参与者活跃度
        const activeParticipants = Array.from(topic.participants).filter(userId => {
            const userActivity = flow.userActivities.get(userId);
            return userActivity && userActivity.isActiveParticipant;
        });
        const participantRatio = activeParticipants.length / Math.max(1, topic.participants.size);

        // 消息密度
        const messageDensity = Math.min(1, topic.messageCount / 10);

        return timeDecay * 0.4 + participantRatio * 0.4 + messageDensity * 0.2;
    }

    /**
     * 计算话题连贯性
     */
    private calculateTopicCoherence(
        topic: TopicAnalysis,
        currentAnalysis: MessageAnalysis,
        flow: ConversationFlow
    ): number {
        // 基于关联类型的连贯性评分
        let coherenceScore = 0.5; // 基础分数

        switch (currentAnalysis.relationType) {
            case "topic_continuation":
                coherenceScore = 0.9;
                break;
            case "topic_shift":
                coherenceScore = 0.6;
                break;
            case "response_to_previous":
                coherenceScore = 0.8;
                break;
            case "side_conversation":
                coherenceScore = 0.4;
                break;
            case "new_topic":
                coherenceScore = 0.1;
                break;
        }

        // 考虑话题稳定性
        const stabilityBonus = topic.stability * 0.2;

        // 考虑参与者一致性
        const participantConsistency = topic.dominantParticipants.length > 0 ?
            topic.dominantParticipants.length / Math.max(1, topic.participants.size) : 0;

        return Math.min(1, coherenceScore + stabilityBonus + participantConsistency * 0.1);
    }

    /**
     * 更新用户活跃度
     */
    private updateUserActivity(flow: ConversationFlow, message: ChatMessage): void {
        const userId = message.sender.id;
        const now = message.timestamp;

        let userActivity = flow.userActivities.get(userId);
        if (!userActivity) {
            userActivity = {
                userId,
                messageCount: 0,
                lastMessageTime: now,
                averageInterval: 0,
                participationScore: 0,
                isActiveParticipant: false,
            };
            flow.userActivities.set(userId, userActivity);
        }

        // 更新消息计数和时间
        const timeSinceLastMessage = now.getTime() - userActivity.lastMessageTime.getTime();
        userActivity.messageCount++;
        userActivity.lastMessageTime = now;

        // 计算平均发言间隔
        if (userActivity.messageCount > 1) {
            userActivity.averageInterval =
                (userActivity.averageInterval * (userActivity.messageCount - 2) + timeSinceLastMessage) /
                (userActivity.messageCount - 1);
        }

        // 计算参与度评分
        userActivity.participationScore = this.calculateParticipationScore(userActivity, flow);

        // 判断是否为活跃参与者
        userActivity.isActiveParticipant = userActivity.participationScore > 0.6;
    }

    /**
     * 计算用户参与度评分
     */
    private calculateParticipationScore(userActivity: UserActivity, flow: ConversationFlow): number {
        const recentTimeWindow = 10 * 60 * 1000; // 10分钟窗口
        const now = Date.now();

        // 时间衰减因子
        const timeSinceLastMessage = now - userActivity.lastMessageTime.getTime();
        const timeDecay = Math.max(0, 1 - timeSinceLastMessage / recentTimeWindow);

        // 消息频率评分
        const totalMessages = flow.recentMessages.length;
        const userMessages = flow.recentMessages.filter(m =>
            flow.userActivities.get(m.messageId.split('_')[0])?.userId === userActivity.userId
        ).length;
        const frequencyScore = totalMessages > 0 ? userMessages / totalMessages : 0;

        // 综合评分
        return (timeDecay * 0.6 + frequencyScore * 0.4);
    }

    /**
     * 更新群组动态
     */
    private updateGroupDynamics(flow: ConversationFlow): void {
        const activeUsers = Array.from(flow.userActivities.values());
        const recentTimeWindow = 5 * 60 * 1000; // 5分钟窗口
        const now = Date.now();

        // 计算活跃参与者
        const activeParticipants = activeUsers.filter(user =>
            now - user.lastMessageTime.getTime() < recentTimeWindow
        );

        flow.groupDynamics.totalParticipants = activeUsers.length;
        flow.groupDynamics.activeParticipants = activeParticipants.length;

        // 计算对话强度
        const recentMessages = flow.recentMessages.filter(msg =>
            now - msg.timestamp.getTime() < recentTimeWindow
        );
        flow.groupDynamics.conversationIntensity = Math.min(1, recentMessages.length / 10);

        // 计算话题切换频率
        const topicSwitches = this.countTopicSwitches(flow.recentMessages.slice(-10));
        flow.groupDynamics.topicSwitchFrequency = topicSwitches / 10;
    }

    /**
     * 计算话题切换次数
     */
    private countTopicSwitches(messages: MessageAnalysis[]): number {
        let switches = 0;
        let currentTopicId: string | undefined = undefined;

        for (const message of messages) {
            if (message.topicId !== currentTopicId) {
                if (currentTopicId !== undefined) {
                    switches++;
                }
                currentTopicId = message.topicId;
            }
        }

        return switches;
    }

    /**
     * 执行内存管理
     */
    private performMemoryManagement(flow: ConversationFlow): void {
        const config = this.config.stateManagement;

        // 限制活跃话题数量
        if (flow.activeTopics.size > config.maxActiveTopics) {
            this.limitActiveTopics(flow, config.maxActiveTopics);
        }

        // 限制用户活跃度记录数量
        if (flow.userActivities.size > config.maxUserActivities) {
            this.limitUserActivities(flow, config.maxUserActivities);
        }

        // 清理过期用户活跃度
        this.cleanupExpiredUserActivities(flow);

        // 合并相似话题
        if (config.enablePerformanceOptimization) {
            this.mergeSimilarTopics(flow);
        }
    }

    /**
     * 限制活跃话题数量
     */
    private limitActiveTopics(flow: ConversationFlow, maxTopics: number): void {
        if (flow.activeTopics.size <= maxTopics) return;

        // 按最后活跃时间排序，保留最新的话题
        const topics = Array.from(flow.activeTopics.entries())
            .sort((a, b) => b[1].lastActivity.getTime() - a[1].lastActivity.getTime())
            .slice(0, maxTopics);

        flow.activeTopics.clear();
        topics.forEach(([id, topic]) => {
            flow.activeTopics.set(id, topic);
        });
    }

    /**
     * 限制用户活跃度记录数量
     */
    private limitUserActivities(flow: ConversationFlow, maxUsers: number): void {
        if (flow.userActivities.size <= maxUsers) return;

        // 按最后消息时间排序，保留最活跃的用户
        const users = Array.from(flow.userActivities.entries())
            .sort((a, b) => b[1].lastMessageTime.getTime() - a[1].lastMessageTime.getTime())
            .slice(0, maxUsers);

        flow.userActivities.clear();
        users.forEach(([id, user]) => {
            flow.userActivities.set(id, user);
        });
    }

    /**
     * 清理过期用户活跃度
     */
    private cleanupExpiredUserActivities(flow: ConversationFlow): void {
        const now = Date.now();
        const timeout = this.config.stateManagement.userActivityTimeoutMs;

        for (const [userId, activity] of flow.userActivities) {
            if (now - activity.lastMessageTime.getTime() > timeout) {
                flow.userActivities.delete(userId);
            }
        }
    }

    /**
     * 合并相似话题
     */
    private mergeSimilarTopics(flow: ConversationFlow): void {
        const topics = Array.from(flow.activeTopics.values());
        const mergeThreshold = this.config.stateManagement.topicMergeThreshold;
        const toMerge: Array<[string, string]> = [];

        // 找出相似的话题对
        for (let i = 0; i < topics.length; i++) {
            for (let j = i + 1; j < topics.length; j++) {
                const similarity = this.calculateTopicSimilarity(
                    topics[i].keywords,
                    topics[j].keywords
                );

                if (similarity > mergeThreshold) {
                    toMerge.push([topics[i].topicId, topics[j].topicId]);
                }
            }
        }

        // 执行合并
        toMerge.forEach(([topicId1, topicId2]) => {
            this.mergeTopics(flow, topicId1, topicId2);
        });
    }

    /**
     * 合并两个话题
     */
    private mergeTopics(flow: ConversationFlow, topicId1: string, topicId2: string): void {
        const topic1 = flow.activeTopics.get(topicId1);
        const topic2 = flow.activeTopics.get(topicId2);

        if (!topic1 || !topic2) return;

        // 合并到较新的话题
        const [mainTopic, mergeTopic] = topic1.lastActivity > topic2.lastActivity
            ? [topic1, topic2]
            : [topic2, topic1];

        // 合并参与者
        mergeTopic.participants.forEach(p => mainTopic.participants.add(p));

        // 合并关键词
        const combinedKeywords = [...new Set([...mainTopic.keywords, ...mergeTopic.keywords])]
            .slice(0, this.config.messageAnalysis.topicKeywordLimit);
        mainTopic.keywords = combinedKeywords;

        // 更新统计信息
        mainTopic.messageCount += mergeTopic.messageCount;
        mainTopic.engagementLevel = Math.max(mainTopic.engagementLevel, mergeTopic.engagementLevel);

        // 删除被合并的话题
        flow.activeTopics.delete(mergeTopic.topicId);
    }

    /**
     * 获取性能指标
     */
    public getPerformanceMetrics(channelId: string): {
        memoryUsage: {
            activeTopics: number;
            userActivities: number;
            recentMessages: number;
        };
        efficiency: {
            topicMergeCount: number;
            cleanupFrequency: number;
        };
    } | null {
        const flow = this.flows.get(channelId);
        if (!flow) return null;

        return {
            memoryUsage: {
                activeTopics: flow.activeTopics.size,
                userActivities: flow.userActivities.size,
                recentMessages: flow.recentMessages.length,
            },
            efficiency: {
                topicMergeCount: 0, // 可以添加计数器跟踪
                cleanupFrequency: 0, // 可以添加计数器跟踪
            },
        };
    }

    /**
     * 清理所有过期数据
     */
    public performGlobalCleanup(): void {
        for (const [channelId, flow] of this.flows) {
            this.cleanupExpiredTopics(flow);
            if (this.config.stateManagement.enableMemoryManagement) {
                this.performMemoryManagement(flow);
            }

            // 如果频道完全没有活动，删除整个流
            if (flow.activeTopics.size === 0 &&
                flow.userActivities.size === 0 &&
                flow.recentMessages.length === 0) {
                this.flows.delete(channelId);
            }
        }
    }

    /**
     * 获取调试信息
     */
    public getDebugInfo(channelId: string): any {
        const flow = this.flows.get(channelId);
        if (!flow) return null;

        return {
            activeTopicsCount: flow.activeTopics.size,
            recentMessagesCount: flow.recentMessages.length,
            conversationPace: flow.conversationPace,
            groupDynamics: flow.groupDynamics,
            userActivities: Array.from(flow.userActivities.values()).map(user => ({
                userId: user.userId,
                messageCount: user.messageCount,
                participationScore: user.participationScore,
                isActiveParticipant: user.isActiveParticipant,
            })),
            topics: Array.from(flow.activeTopics.values()).map((topic) => ({
                id: topic.topicId,
                status: topic.status,
                messageCount: topic.messageCount,
                participantsCount: topic.participants.size,
                keywords: topic.keywords,
                dominantParticipants: topic.dominantParticipants,
                engagementLevel: topic.engagementLevel,
                topicCoherence: topic.topicCoherence,
            })),
            performanceMetrics: this.getPerformanceMetrics(channelId),
        };
    }
}
