### `3.0.0-beta.1`

这是一个包含大量底层重构和核心功能新增的版本，为未来的发展奠定了坚实的基础。

**✨ 新功能 (Features)**
- **多模态支持**: 实现了图片与文本的混合输入处理，允许模型理解和响应包含图片的消息。
- **世界状态管理**: 引入了全新的“世界状态”服务，用于管理频道、成员和对话回合的持久化数据。
- **配置管理命令**: 新增 `conf.get` 和 `conf.set` 命令，方便管理员在运行时动态获取和修改配置。
- **转发消息处理**: 新增了获取和查看转发聊天记录的功能，包括其中的图片内容。
- **提示词构造器升级**: 提示词（Prompt）现在支持 `{{}}` 占位符、自定义模板和更强的防越狱机制。

**💥 重大变更与核心重构 (Breaking Changes & Refactoring)**
- **项目结构调整 (BREAKING CHANGE)**: 对项目结构进行了重大调整，优化了类型定义、模块划分和导入路径，提升了代码的内聚性和可维护性。
- **交互模型重构**: 废弃了原有的 `ScenarioManager`，重构为基于 **回合制的世界状态系统**，使长期记忆和上下文管理更加健壮和清晰。
- **工具扩展系统重构**: 引入了基于 **装饰器** 的新架构，极大地简化了工具（Tool）和扩展的定义与注册流程。
- **核心服务与中间件重构**:
    - 统一了所有内部服务的命名规范（使用 `yesimbot` 前缀）。
    - 将中间件重构为抽象类，优化了依赖注入方式，简化了服务容器。
- **场景模块优化**: 重构了场景代码结构，新增了上下文处理器，并改进了上下文摘要的渲染逻辑。

---

### `3.0.0-beta.0`

此版本重点重构了记忆系统，并正式引入了识图功能。

**✨ 新功能 (Features)**
- **记忆压缩**: 新增长期记忆自动压缩功能，可根据消息数量、时间间隔等条件触发，将多条对话压缩为一条摘要，并支持压缩前自动备份。
- **模型识图**: 新增了图片查看工具，允许模型接收并“理解”图片内容。
- **错误上报**: 调整了错误上报的格式，使其更易于分析。

**🔄 优化与重构 (Optimizations & Refactoring)**
- **记忆模块重构**: 对记忆系统进行了彻底重构，将其拆分为模块化组件，并实现了基于数据库的存储、归档和文件同步功能。
- **模型服务重构**: 将模型切换器（ChatModelSwitcher）与核心服务分离，使模型管理更加灵活。

---

### `3.0.0-alpha.16`

**🔄 优化与重构 (Optimizations & Refactoring)**
- 将工具管理器（ToolManager）重构为标准的 Koishi 插件服务，通过上下文注入使用，提升了集成性和代码健壮性。

**🐞 修复 (Fixes)**
- 修复了语言文件的导入问题。

---

### `3.0.0-alpha.15`

**✨ 新功能与优化 (Features & Optimizations)**
- 优化了场景管理和响应处理逻辑，支持按频道分组和动态调整回复概率。
- 增强了多场景协同处理能力。
- 新增了 `最大心跳次数` 的配置项。

**🔄 优化与重构 (Optimizations & Refactoring)**
- 将词向量嵌入（Embeddings）功能重构到适配器模块中。

**🐞 修复 (Fixes)**
- 为部分命令添加了缺失的参数验证。
- 修复了扩展的导入路径问题及一些内置工具的缺陷。

---

### `3.0.0-alpha.14`

此版本专注于场景管理和平台适配的架构重构。

**🔄 核心重构 (Core Refactoring)**
- 引入 `ScenarioManager` 服务，用于集中管理场景的生命周期和缓存，实现了逻辑解耦。
- 引入 `PlatformAdapter` 接口，以更好地支持多平台信息适配。
- 优化了交互记录的处理方式和错误日志的输出格式。

---

### `3.0.0-alpha.13`

**✨ 新功能 (Features)**
- **适配器增强**:
    - 添加了对更多平台适配器的支持。
    - 支持为适配器设置代理服务器。
    - 实现 API 请求失败后自动切换到下一个可用 API 的容错机制。
- **体验优化**:
    - 优化了提示词（Prompt）以尝试解决刷屏和复读问题。
    - 使用 MD5 作为图片缓存键，提升效率。
    - 将消息 ID 添加到交互上下文中，便于追踪。

---

### `3.0.0-alpha.12`

**🐞 修复 (Fixes)**
- 修复了在响应失败后，状态没有被正确重置的问题。

---

### `3.0.0-alpha.11`

**✨ 新功能与优化 (Features & Optimizations)**
- 为 API 调用和工具执行添加了 **超时与重试机制**，并允许配置重试次数。
- 增加了对图片消息的基础处理能力。
- 优化了应用的初始化流程。

---

### `3.0.0-alpha.10`

**✨ 新功能与优化 (Features & Optimizations)**
- **智能回复逻辑**: 优化了回复触发条件，引入用户优先级和频道整体意愿值，使回复决策更智能。
- **新增管理指令**: 添加了清除上下文、清空图片缓存和管理扩展的命令。
- **服务化**: 将 YesImBot 自身注册为 Koishi 服务，便于其他插件调用和集成。

---

### `3.0.0-alpha.9`

**🐞 修复 (Fixes)**
- 修复了因响应失败导致后续不再触发回复的严重问题。
- 更改了错误的内部上报地址。

**🔄 调整 (Adjustments)**
- 将工具扩展（tools）移动到了内置（builtin）目录。

---

### `3.0.0-alpha.8`

**🔄 调整与修复 (Adjustments & Fixes)**
- 修正了部分内置工具的参数定义。