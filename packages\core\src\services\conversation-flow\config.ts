/**
 * ConversationFlowAnalyzer 统一配置管理
 *
 * 提供所有模块的默认配置、配置验证和配置合并功能
 */

import {
    TopicAnalyzerConfig,
    UserActivityTrackerConfig,
    PaceAnalyzerConfig,
    StateManagerConfig,
    DecisionEngineConfig,
    IntelligenceConfig
} from './interfaces';

// ==================== 常量定义 ====================

/**
 * 时间常量（毫秒）
 */
export const TIME_CONSTANTS = {
    ONE_SECOND_MS: 1000,
    ONE_MINUTE_MS: 60 * 1000,
    FIVE_MINUTES_MS: 5 * 60 * 1000,
    TEN_MINUTES_MS: 10 * 60 * 1000,
    THIRTY_MINUTES_MS: 30 * 60 * 1000,
    ONE_HOUR_MS: 60 * 60 * 1000,
} as const;

/**
 * 文本处理常量
 */
export const TEXT_PROCESSING_CONSTANTS = {
    CHINESE_CHAR_RANGE: '\u4e00-\u9fa5',
    TEXT_CLEANING_REGEX: '[^\u4e00-\u9fa5a-zA-Z0-9\s]',
    WHITESPACE_NORMALIZATION_REGEX: '\s+',
    TOPIC_ID_RANDOM_LENGTH: 9,
    KEYWORD_MIN_LENGTH: 2,
    MAX_INTERESTS_PER_USER: 10,
} as const;

/**
 * 通用停用词列表
 */
export const COMMON_STOP_WORDS = [
    // 中文停用词
    '的', '了', '是', '在', '我', '你', '他', '她', '它', '这', '那',
    '有', '和', '与', '就', '都', '要', '会', '能', '可以', '不是',
    '一个', '没有', '什么', '怎么', '为什么', '哪里', '谁', '何时',
    // 英文停用词
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to',
    'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be',
    'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'must',
    'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he',
    'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them'
] as const;

// ==================== 默认配置 ====================

/**
 * 话题分析器默认配置
 */
export const DEFAULT_TOPIC_ANALYZER_CONFIG: TopicAnalyzerConfig = {
    // 基础限制配置
    maxActiveTopicsCount: 5,
    keywordExtractionLimit: 10,

    // 话题状态阈值配置
    minMessagesForStableThreshold: 5,
    minParticipantsForStableThreshold: 2,

    // 时间配置（毫秒）
    topicTimeoutMs: TIME_CONSTANTS.TEN_MINUTES_MS,
    topicCoolingMs: TIME_CONSTANTS.FIVE_MINUTES_MS,
    recentActivityWindowMs: TIME_CONSTANTS.FIVE_MINUTES_MS,

    // 相似度和合并配置
    topicSimilarityThreshold: 0.7,
    topicMergeThreshold: 0.8,

    // 稳定性计算权重
    stabilityWeights: {
        ageWeight: 0.5,
        participantWeight: 0.3,
        timeWeight: 0.2,
        baseStability: 0.1,
    },

    // 参与度计算配置
    engagementCalculation: {
        messageFrequencyDivisor: 10,
        recentActivityWeight: 0.4,
        messageFrequencyWeight: 0.6,
    },

    // 连贯性调整配置
    coherenceAdjustments: {
        continuationBonus: 0.1,
        shiftPenalty: 0.1,
        minCoherence: 0.3,
    },

    // 功能开关
    enableSemanticAnalysis: true,
    enableTopicMerging: true,

    // 文本处理配置
    textProcessing: {
        stopWords: [...COMMON_STOP_WORDS],
        regexPatterns: {
            textCleaning: TEXT_PROCESSING_CONSTANTS.TEXT_CLEANING_REGEX,
            whitespaceNormalization: TEXT_PROCESSING_CONSTANTS.WHITESPACE_NORMALIZATION_REGEX,
        },
        topicIdRandomLength: TEXT_PROCESSING_CONSTANTS.TOPIC_ID_RANDOM_LENGTH,
    },
};

/**
 * 用户活跃度跟踪器默认配置
 */
export const DEFAULT_USER_ACTIVITY_TRACKER_CONFIG: UserActivityTrackerConfig = {
    // 容量限制配置
    maxUserProfilesCount: 100,
    maxInterestsPerUser: TEXT_PROCESSING_CONSTANTS.MAX_INTERESTS_PER_USER,
    maxInfluencersCount: 5,

    // 时间配置（毫秒）
    userProfileTimeoutMs: TIME_CONSTANTS.THIRTY_MINUTES_MS,
    activityAnalysisWindowMs: TIME_CONSTANTS.TEN_MINUTES_MS,
    conversationIntensityWindowMs: TIME_CONSTANTS.FIVE_MINUTES_MS,
    peakActivityDetectionWindowMs: TIME_CONSTANTS.TEN_MINUTES_MS,

    // 阈值配置
    influenceScoreThreshold: 0.7,
    peakActivityIntensityThreshold: 0.8,
    minMessagesForProfileThreshold: 3,

    // 衰减和权重配置
    engagementDecayRate: 0.95,
    participationScoreWeights: {
        frequencyWeight: 0.3,
        recencyWeight: 0.25,
        consistencyWeight: 0.25,
        influenceWeight: 0.2,
    },

    // 参与度计算配置
    participationCalculation: {
        maxMessageCountForFrequency: 50,
        idealResponseIntervalMs: TIME_CONSTANTS.FIVE_MINUTES_MS,
        conversationIntensityMultiplier: 2,
        topicSwitchFrequencyDivisor: 10,
    },

    // 交流风格判断配置
    communicationStyleThresholds: {
        activeMessageCountThreshold: 20,
        activeIntervalThresholdMs: TIME_CONSTANTS.ONE_MINUTE_MS,
        passiveMessageCountThreshold: 5,
        passiveIntervalThresholdMs: TIME_CONSTANTS.FIVE_MINUTES_MS,
    },

    // 影响力计算配置
    influenceCalculation: {
        rankScoreWeight: 0.6,
        activityScoreWeight: 0.4,
        activeStyleMultiplier: 1.2,
        passiveStyleMultiplier: 1.0,
        lurkerStyleMultiplier: 0.8,
    },

    // 趋势分析配置
    trendAnalysisThresholds: {
        increasingRecencyThreshold: 0.8,
        increasingParticipationThreshold: 0.7,
        decreasingRecencyThreshold: 0.3,
        decreasingParticipationThreshold: 0.3,
    },

    // 文本处理配置
    textProcessing: {
        stopWords: [...COMMON_STOP_WORDS],
        keywordMinLength: TEXT_PROCESSING_CONSTANTS.KEYWORD_MIN_LENGTH,
        regexPatterns: {
            textCleaning: TEXT_PROCESSING_CONSTANTS.TEXT_CLEANING_REGEX,
            whitespaceNormalization: TEXT_PROCESSING_CONSTANTS.WHITESPACE_NORMALIZATION_REGEX,
        },
    },
};

/**
 * 节奏分析器默认配置
 */
export const DEFAULT_PACE_ANALYZER_CONFIG: PaceAnalyzerConfig = {
    // 分析窗口配置
    analysisWindows: {
        shortTermWindowMs: 2 * TIME_CONSTANTS.ONE_MINUTE_MS,
        mediumTermWindowMs: TIME_CONSTANTS.FIVE_MINUTES_MS,
        longTermWindowMs: TIME_CONSTANTS.TEN_MINUTES_MS,
        paceCalculationWindowSize: 6,
    },

    // 消息数量阈值
    messageCountThresholds: {
        minMessagesForAnalysis: 2,
        burstDetectionThreshold: 3,
        burstTimeWindowMs: 30 * TIME_CONSTANTS.ONE_SECOND_MS,
    },

    // 节奏分类阈值（毫秒）
    paceClassificationThresholds: {
        fastPaceThresholdMs: 5 * TIME_CONSTANTS.ONE_SECOND_MS,
        slowPaceThresholdMs: 30 * TIME_CONSTANTS.ONE_SECOND_MS,
        fastPaceMultiplier: 2,
        slowPaceMultiplier: 2,
    },

    // 动态阈值调整配置
    dynamicThresholdAdjustment: {
        groupSizeMultiplierBase: 1.0,
        groupSizeMultiplierStep: 0.1,
        maxGroupSizeMultiplier: 2.0,
        minGroupSizeMultiplier: 0.5,
        topicSwitchMultiplier: 0.5,
    },

    // 参与度影响配置
    participationFactorWeights: {
        participationRatioWeight: 0.6,
        conversationIntensityWeight: 0.4,
    },

    // 节奏评估权重
    paceEvaluationWeights: {
        burstPenaltyMultiplier: 0.5,
        variabilityPenaltyThreshold: 1.0,
        variabilityPenaltyMultiplier: 0.8,
        lowParticipationBonus: 1.2,
        highParticipationPenalty: 0.9,
        accelerationPenalty: 0.8,
        decelerationBonus: 1.1,
    },
};

/**
 * 状态管理器默认配置
 */
export const DEFAULT_STATE_MANAGER_CONFIG: StateManagerConfig = {
    // 内存管理配置
    memoryManagement: {
        maxActiveTopicsPerChannel: 5,
        maxUserActivitiesPerChannel: 50,
        maxRecentMessagesCount: 15,
        cleanupIntervalMs: TIME_CONSTANTS.FIVE_MINUTES_MS,
    },

    // 持久化配置
    persistence: {
        enableStatePersistence: false,
        snapshotIntervalMs: TIME_CONSTANTS.TEN_MINUTES_MS,
        maxSnapshotsCount: 10,
        compressionEnabled: true,
    },

    // 性能监控配置
    performanceMonitoring: {
        enableMetricsCollection: true,
        metricsUpdateIntervalMs: TIME_CONSTANTS.ONE_MINUTE_MS,
        maxMetricsHistoryCount: 100,
    },

    // 缓存配置
    caching: {
        enableCaching: true,
        cacheExpirationMs: TIME_CONSTANTS.TEN_MINUTES_MS,
        maxCacheSize: 1000,
        cacheHitRateThreshold: 0.8,
    },
};

/**
 * 决策引擎默认配置
 */
export const DEFAULT_DECISION_ENGINE_CONFIG: DecisionEngineConfig = {
    // 决策阈值配置
    decisionThresholds: {
        replyConfidenceThreshold: 0.6,
        highPriorityThreshold: 0.8,
        mediumPriorityThreshold: 0.5,
    },

    // 等待时间配置（毫秒）
    waitTimeConfiguration: {
        baseWaitTimeMs: 3 * TIME_CONSTANTS.ONE_SECOND_MS,
        quickResponseTimeMs: TIME_CONSTANTS.ONE_SECOND_MS,
        minWaitTimeMs: TIME_CONSTANTS.ONE_SECOND_MS,
        maxWaitTimeMs: 8 * TIME_CONSTANTS.ONE_SECOND_MS,
        directMentionWaitTimeMs: TIME_CONSTANTS.ONE_SECOND_MS,
    },

    // 因子权重配置
    decisionFactorWeights: {
        topicAnalysisWeight: 0.4,
        activityAnalysisWeight: 0.3,
        paceAnalysisWeight: 0.2,
        contextWeight: 0.1,
    },

    // 规则引擎配置
    ruleEngine: {
        enableRuleBasedDecision: true,
        maxActiveRulesCount: 20,
        ruleEvaluationTimeoutMs: TIME_CONSTANTS.ONE_SECOND_MS,
    },

    // 学习配置
    learningConfiguration: {
        enableFeedbackLearning: false,
        feedbackWeightDecay: 0.9,
        maxFeedbackHistoryCount: 100,
    },
};

// ==================== 配置验证和合并工具 ====================

/**
 * 配置验证错误
 */
export class ConfigValidationError extends Error {
    constructor(message: string, public field: string, public value: any) {
        super(`Configuration validation error for field '${field}': ${message}`);
        this.name = 'ConfigValidationError';
    }
}

/**
 * 验证数值范围
 */
function validateNumberRange(value: number, min: number, max: number, fieldName: string): void {
    if (typeof value !== 'number' || isNaN(value)) {
        throw new ConfigValidationError(`Must be a valid number`, fieldName, value);
    }
    if (value < min || value > max) {
        throw new ConfigValidationError(`Must be between ${min} and ${max}`, fieldName, value);
    }
}

/**
 * 验证正数
 */
function validatePositiveNumber(value: number, fieldName: string): void {
    if (typeof value !== 'number' || isNaN(value) || value <= 0) {
        throw new ConfigValidationError(`Must be a positive number`, fieldName, value);
    }
}

/**
 * 验证非负数
 */
function validateNonNegativeNumber(value: number, fieldName: string): void {
    if (typeof value !== 'number' || isNaN(value) || value < 0) {
        throw new ConfigValidationError(`Must be a non-negative number`, fieldName, value);
    }
}

/**
 * 验证话题分析器配置
 */
export function validateTopicAnalyzerConfig(config: Partial<TopicAnalyzerConfig>): void {
    if (config.maxActiveTopicsCount !== undefined) {
        validatePositiveNumber(config.maxActiveTopicsCount, 'maxActiveTopicsCount');
    }

    if (config.keywordExtractionLimit !== undefined) {
        validatePositiveNumber(config.keywordExtractionLimit, 'keywordExtractionLimit');
    }

    if (config.topicSimilarityThreshold !== undefined) {
        validateNumberRange(config.topicSimilarityThreshold, 0, 1, 'topicSimilarityThreshold');
    }

    if (config.topicMergeThreshold !== undefined) {
        validateNumberRange(config.topicMergeThreshold, 0, 1, 'topicMergeThreshold');
    }

    if (config.topicTimeoutMs !== undefined) {
        validatePositiveNumber(config.topicTimeoutMs, 'topicTimeoutMs');
    }

    if (config.topicCoolingMs !== undefined) {
        validatePositiveNumber(config.topicCoolingMs, 'topicCoolingMs');
    }
}

/**
 * 验证用户活跃度跟踪器配置
 */
export function validateUserActivityTrackerConfig(config: Partial<UserActivityTrackerConfig>): void {
    if (config.maxUserProfilesCount !== undefined) {
        validatePositiveNumber(config.maxUserProfilesCount, 'maxUserProfilesCount');
    }

    if (config.influenceScoreThreshold !== undefined) {
        validateNumberRange(config.influenceScoreThreshold, 0, 1, 'influenceScoreThreshold');
    }

    if (config.engagementDecayRate !== undefined) {
        validateNumberRange(config.engagementDecayRate, 0, 1, 'engagementDecayRate');
    }

    if (config.userProfileTimeoutMs !== undefined) {
        validatePositiveNumber(config.userProfileTimeoutMs, 'userProfileTimeoutMs');
    }

    if (config.participationScoreWeights !== undefined) {
        const weights = config.participationScoreWeights;
        const totalWeight = (weights.frequencyWeight || 0) +
                           (weights.recencyWeight || 0) +
                           (weights.consistencyWeight || 0) +
                           (weights.influenceWeight || 0);

        if (Math.abs(totalWeight - 1.0) > 0.01) {
            throw new ConfigValidationError(
                `Participation score weights must sum to 1.0, got ${totalWeight}`,
                'participationScoreWeights',
                weights
            );
        }
    }
}

/**
 * 深度合并配置对象
 */
function deepMergeConfig<T extends Record<string, any>>(
    defaultConfig: T,
    userConfig: Partial<T>
): T {
    const result = { ...defaultConfig };

    for (const key in userConfig) {
        if (userConfig.hasOwnProperty(key)) {
            const userValue = userConfig[key];
            const defaultValue = defaultConfig[key];

            if (userValue !== undefined) {
                if (typeof userValue === 'object' &&
                    userValue !== null &&
                    !Array.isArray(userValue) &&
                    typeof defaultValue === 'object' &&
                    defaultValue !== null &&
                    !Array.isArray(defaultValue)) {
                    // 递归合并嵌套对象
                    result[key] = deepMergeConfig(defaultValue, userValue);
                } else {
                    // 直接覆盖
                    result[key] = userValue;
                }
            }
        }
    }

    return result;
}

/**
 * 合并话题分析器配置
 */
export function mergeTopicAnalyzerConfig(
    userConfig: Partial<TopicAnalyzerConfig> = {}
): TopicAnalyzerConfig {
    validateTopicAnalyzerConfig(userConfig);
    return deepMergeConfig(DEFAULT_TOPIC_ANALYZER_CONFIG, userConfig);
}

/**
 * 合并用户活跃度跟踪器配置
 */
export function mergeUserActivityTrackerConfig(
    userConfig: Partial<UserActivityTrackerConfig> = {}
): UserActivityTrackerConfig {
    validateUserActivityTrackerConfig(userConfig);
    return deepMergeConfig(DEFAULT_USER_ACTIVITY_TRACKER_CONFIG, userConfig);
}

/**
 * 合并节奏分析器配置
 */
export function mergePaceAnalyzerConfig(
    userConfig: Partial<PaceAnalyzerConfig> = {}
): PaceAnalyzerConfig {
    return deepMergeConfig(DEFAULT_PACE_ANALYZER_CONFIG, userConfig);
}

/**
 * 合并状态管理器配置
 */
export function mergeStateManagerConfig(
    userConfig: Partial<StateManagerConfig> = {}
): StateManagerConfig {
    return deepMergeConfig(DEFAULT_STATE_MANAGER_CONFIG, userConfig);
}

/**
 * 合并决策引擎配置
 */
export function mergeDecisionEngineConfig(
    userConfig: Partial<DecisionEngineConfig> = {}
): DecisionEngineConfig {
    return deepMergeConfig(DEFAULT_DECISION_ENGINE_CONFIG, userConfig);
}
