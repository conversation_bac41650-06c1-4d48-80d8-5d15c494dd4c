import { Context } from "koishi";
import { Config } from "../../config";
import { ServiceManager } from "../../services/ServiceManager";
import { createBuiltinMiddlewares } from "../registry/BuiltinMiddlewares";
import { MiddlewarePipeline } from "./MiddlewarePipeline";
import { MiddlewareRegistry } from "./MiddlewareRegistry";
import { ReplyConditionConfig } from "../impl/ReplyCondition";
import { LLMProcessingConfig } from "../impl/LLMProcessing";

/**
 * 重构后的中间件配置器
 * 基于注册表和管道的灵活架构
 */
export class MiddlewareConfigurator {
    private registry: MiddlewareRegistry;
    private pipeline: MiddlewarePipeline;

    constructor(private ctx: Context, private config: Config, private services: ServiceManager) {
        this.registry = new MiddlewareRegistry(ctx);
        this.pipeline = new MiddlewarePipeline(ctx, this.registry);
    }

    /**
     * 配置并返回中间件管道
     */
    async configure(): Promise<MiddlewarePipeline> {
        // 1. 注册内置中间件
        await this.registerBuiltinMiddlewares();

        // 2. 注册自定义中间件
        await this.registerCustomMiddlewares();

        // 3. 构建执行管道
        await this.buildPipeline();

        // 4. 注册清理处理器
        this.registerCleanupHandlers();

        return this.pipeline;
    }

    /**
     * 注册内置中间件
     */
    private async registerBuiltinMiddlewares(): Promise<void> {
        const definitions = createBuiltinMiddlewares();

        for (const [id, definition] of definitions) {
            this.registry.register(id, definition);
        }

        this.ctx.logger.debug(`注册了 ${definitions.size} 个内置中间件`);
    }

    /**
     * 注册自定义中间件
     */
    private async registerCustomMiddlewares(): Promise<void> {
        // 这里可以扫描自定义中间件目录或从配置中加载
        // 暂时为空，后续可以扩展
        this.ctx.logger.debug("自定义中间件注册完成");
    }

    /**
     * 构建执行管道
     */
    private async buildPipeline(): Promise<void> {
        // 按照配置添加中间件到管道
        this.pipeline
            .add("builtin.error-handling", this.buildErrorHandlingConfig())
            .add("builtin.database-storage", this.buildDatabaseStorageConfig())
            .add("builtin.reply-condition", this.buildReplyConditionConfig())
            .add("builtin.llm-processing", this.buildLLMProcessingConfig())
            .add("builtin.response-handling", this.buildResponseHandlingConfig());

        // 构建管道
        await this.pipeline.build();
    }

    /**
     * 构建错误处理配置
     */
    private buildErrorHandlingConfig() {
        return {
            debug: this.config.Debug.EnableDebug,
            uploadDump: this.config.Debug.UploadDump,
            pasteServiceUrl: "https://dump.yesimbot.chat/",
            includeFullSessionContent: false,
        };
    }

    /**
     * 构建数据库存储配置
     */
    private buildDatabaseStorageConfig() {
        return {
            enableImageProcessing: true,
            enableWorldStateUpdate: true,
            batchSize: 10,
            timeout: 5000,
        };
    }

    /**
     * 构建回复条件配置
     */
    private buildReplyConditionConfig(): ReplyConditionConfig {
        const replyConfig = this.config.ReplyCondition;
        return replyConfig;
    }

    /**
     * 构建LLM处理配置
     */
    private buildLLMProcessingConfig(): LLMProcessingConfig {
        return {
            Debug: this.config.Debug.EnableDebug,
            RetryConfig: this.config.LLM.RetryConfig,
            AdapterSwitchingConfig: this.config.LLM.AdapterSwitching,
            Timeout: 60000,
            EnableStreaming: true,
        };
    }

    /**
     * 构建响应处理配置
     */
    private buildResponseHandlingConfig() {
        return {
            maxRetry: this.config.ToolCall.MaxRetry,
            life: this.config.ToolCall.Life,
            maxHeartbeat: this.config.Chat.MaxHeartbeat,
            enableToolValidation: true,
            parallelExecution: false,
        };
    }

    /**
     * 注册清理处理器
     */
    private registerCleanupHandlers(): void {
        this.ctx.on("dispose", async () => {
            await this.pipeline.dispose();
            await this.registry.dispose();
        });
    }
}
